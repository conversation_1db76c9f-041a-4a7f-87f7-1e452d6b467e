@tailwind base;
@tailwind components;
@tailwind utilities;

/* Modern Fintech Design System - All colors MUST be HSL */

@layer base {
  :root {
    /* Base colors */
    --background: 240 10% 98%;
    --foreground: 240 10% 8%;

    --card: 0 0% 100%;
    --card-foreground: 240 10% 8%;

    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 8%;

    /* Primary brand - Deep blue */
    --primary: 240 100% 12%;
    --primary-foreground: 0 0% 98%;

    /* Secondary - Light gray */
    --secondary: 240 5% 96%;
    --secondary-foreground: 240 10% 12%;

    --muted: 240 5% 96%;
    --muted-foreground: 240 4% 46%;

    --accent: 240 5% 96%;
    --accent-foreground: 240 10% 12%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 6% 90%;
    --input: 240 6% 90%;
    --ring: 240 100% 12%;

    --radius: 1rem;

    /* Fintech specific colors */
    --success: 142 76% 36%;
    --success-foreground: 0 0% 98%;
    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 98%;
    --info: 200 100% 50%;
    --info-foreground: 0 0% 98%;

    /* Financial gradients */
    --gradient-growth: linear-gradient(135deg, hsl(142 76% 36%), hsl(167 85% 65%));
    --gradient-risk: linear-gradient(135deg, hsl(38 92% 50%), hsl(0 84% 60%));
    --gradient-premium: linear-gradient(135deg, hsl(240 100% 12%), hsl(260 100% 20%));
    --gradient-neutral: linear-gradient(135deg, hsl(240 5% 85%), hsl(240 5% 95%));

    /* Glass morphism */
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
    --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-glow: 0 0 20px rgba(142, 76, 36, 0.3);

    /* Animations */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --transition-elastic: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    /* Dark mode - Premium fintech aesthetic */
    --background: 258 28% 6%;
    --foreground: 300 20% 95%;

    --card: 258 30% 8%;
    --card-foreground: 300 20% 95%;

    --popover: 258 30% 8%;
    --popover-foreground: 300 20% 95%;

    /* Primary - Bright purple accent */
    --primary: 270 100% 70%;
    --primary-foreground: 258 28% 6%;

    /* Secondary - Dark purple */
    --secondary: 258 30% 12%;
    --secondary-foreground: 300 20% 90%;

    --muted: 258 30% 12%;
    --muted-foreground: 300 10% 60%;

    --accent: 320 80% 60%;
    --accent-foreground: 258 28% 6%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;

    --border: 258 30% 15%;
    --input: 258 30% 15%;
    --ring: 270 100% 70%;

    /* Dark mode financial gradients */
    --gradient-growth: linear-gradient(135deg, hsl(142 76% 36%), hsl(167 85% 45%));
    --gradient-risk: linear-gradient(135deg, hsl(38 92% 50%), hsl(0 84% 50%));
    --gradient-premium: linear-gradient(135deg, hsl(270 100% 70%), hsl(320 80% 60%));
    --gradient-neutral: linear-gradient(135deg, hsl(258 30% 12%), hsl(258 30% 18%));

    /* Enhanced glass morphism for dark mode */
    --glass-bg: rgba(75, 0, 130, 0.1);
    --glass-border: rgba(147, 51, 234, 0.3);
    --glass-shadow: 0 8px 32px 0 rgba(147, 51, 234, 0.2);

    /* Dark shadows with purple glow */
    --shadow-glow: 0 0 30px rgba(147, 51, 234, 0.4);

    --sidebar-background: 258 30% 8%;
    --sidebar-foreground: 300 20% 90%;
    --sidebar-primary: 270 100% 70%;
    --sidebar-primary-foreground: 258 28% 6%;
    --sidebar-accent: 258 30% 12%;
    --sidebar-accent-foreground: 300 20% 90%;
    --sidebar-border: 258 30% 15%;
    --sidebar-ring: 270 100% 70%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-inter;
    /* Reserve scrollbar space to prevent layout shift when modals open */
    scrollbar-gutter: stable both-edges;
  }
}

@layer components {
  /* Glass morphism utility */
  .glass {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
  }

  /* Financial metric gradients */
  .gradient-growth {
    background: var(--gradient-growth);
  }

  .gradient-risk {
    background: var(--gradient-risk);
  }

  .gradient-premium {
    background: var(--gradient-premium);
  }

  .gradient-neutral {
    background: var(--gradient-neutral);
  }
  
  /* Badge Rarity Classes */
  .gradient-common { 
    background: linear-gradient(135deg, hsl(210 40% 50%), hsl(210 40% 40%)); 
  }
  .gradient-rare { 
    background: linear-gradient(135deg, hsl(210 100% 50%), hsl(210 100% 40%)); 
  }
  .gradient-epic { 
    background: linear-gradient(135deg, hsl(270 100% 50%), hsl(270 100% 40%)); 
  }
  .gradient-legendary { 
    background: linear-gradient(135deg, hsl(45 100% 50%), hsl(45 100% 40%)); 
  }

  /* Smooth transitions */
  .transition-smooth {
    transition: var(--transition-smooth);
  }

  .transition-bounce {
    transition: var(--transition-bounce);
  }

  /* Financial card styles */
  .financial-card {
    @apply bg-card/80 backdrop-blur-sm border border-border/50 rounded-2xl p-6 transition-smooth hover:shadow-lg hover:border-border;
  }

  .financial-card-dark {
    @apply glass rounded-2xl p-6 transition-smooth hover:shadow-glow;
  }

  /* Chart container */
  .chart-container {
    @apply relative w-full h-64 p-4 bg-gradient-to-br from-background to-secondary/30 rounded-xl border border-border/30;
  }

  /* Metric display */
  .metric-large {
    @apply text-4xl font-bold tracking-tight bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent;
  }

  .metric-medium {
    @apply text-2xl font-semibold text-foreground;
  }

  .metric-small {
    @apply text-sm font-medium text-muted-foreground uppercase tracking-wide;
  }

  /* Status indicators */
  .status-success {
    @apply inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-success/10 text-success border border-success/20;
  }

  .status-warning {
    @apply inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-warning/10 text-warning border border-warning/20;
  }

  .status-info {
    @apply inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-info/10 text-info border border-info/20;
  }

  /* Navigation styles */
  .nav-item {
    @apply flex items-center space-x-3 px-4 py-3 rounded-xl text-muted-foreground hover:text-foreground hover:bg-secondary/50 transition-smooth;
  }

  .nav-item-active {
    @apply flex items-center space-x-3 px-4 py-3 rounded-xl text-primary bg-primary/10 border border-primary/20;
  }

  /* Global Hover Effects */
  .hover-scale {
    @apply transition-transform duration-200 hover:scale-105;
  }

  .hover-glow:hover {
    box-shadow: 0 0 20px var(--primary-glow);
  }

  /* Global Hover Zoom for Interactive Elements */
  @media (hover: hover) and (pointer: fine) {
    button:hover,
    a:hover,
    .glass:hover,
    [role="button"]:hover {
      transform: scale(1.02);
      transition: all 0.2s ease-out;
    }

    /* Opt-out: containers with .no-hover-zoom should never scale on hover */
    .no-hover-zoom:hover,
    .no-hover-zoom *:hover {
      transform: none !important;
    }
  }

  /* Card hover effects */
  .glass:hover {
    backdrop-filter: blur(25px);
    border-color: hsl(var(--primary) / 0.3);
    box-shadow: 0 8px 32px hsl(var(--primary) / 0.15);
  }

  /* Touch devices: avoid heavy hover effects */
  @media (hover: none) {
    .glass:hover {
      backdrop-filter: blur(10px);
      border-color: var(--glass-border);
      box-shadow: var(--glass-shadow);
    }
  }

  /* Fade transition for route changes */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  /* Respect reduced motion preferences */
  @media (prefers-reduced-motion: reduce) {
    * {
      animation-duration: 0.001ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.001ms !important;
      scroll-behavior: auto !important;
    }
  }
}